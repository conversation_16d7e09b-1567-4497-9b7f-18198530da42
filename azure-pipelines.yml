trigger:
  branches:
    include:
      - main

variables:
  imageName: verdaccio-server

pool:
  vmImage: 'ubuntu-latest'

steps:
- task: DockerInstaller@0
  displayName: 'Install Docker'

- task: Docker@2
  displayName: 'Build Docker Image'
  inputs:
    command: build
    Dockerfile: '**/Dockerfile'
    tags: |
      $(imageName):$(Build.BuildId)

- task: Docker@2
  displayName: 'Push to Azure Container Registry'
  inputs:
    command: push
    tags: |
      $(imageName):$(Build.BuildId)
    containerRegistry: '<YOUR-AZURE-CONTAINER-REGISTRY-SERVICE-CONNECTION>'

- task: AzureWebAppContainer@1
  displayName: 'Deploy to Azure Web App for Containers'
  inputs:
    azureSubscription: '<YOUR-AZURE-SUBSCRIPTION-SERVICE-CONNECTION>'
    appName: '<YOUR-WEBAPP-NAME>'
    containers: '<ACR-NAME>.azurecr.io/$(imageName):$(Build.BuildId)'
