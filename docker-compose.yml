services:
  verdaccio:
    build: .
    container_name: verdaccio-local
    ports:
      - "4873:4873"
    volumes:
      - verdaccio_storage:/verdaccio/storage
    environment:
      - VERDACCIO_PUBLIC_URL=http://localhost/packages
    labels:
      - "org.opencontainers.image.title=Verdaccio Registry (Direct Access)"
      - "org.opencontainers.image.description=Direct access to Verdaccio - Use proxy at :8080/packages/ instead"
    restart: unless-stopped
    networks:
      - verdaccio-network

  nginx:
    image: nginx:alpine
    container_name: verdaccio-proxy
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    labels:
      - "org.opencontainers.image.title=Verdaccio Proxy (/packages path)"
      - "org.opencontainers.image.description=Access at localhost/packages/ - Main entry point"
    depends_on:
      - verdaccio
    networks:
      - verdaccio-network

volumes:
  verdaccio_storage:

networks:
  verdaccio-network:
    driver: bridge 