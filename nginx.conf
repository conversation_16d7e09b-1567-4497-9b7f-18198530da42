events {
    worker_connections 1024;
}

http {
    upstream verdaccio {
        server 127.0.0.1:4873;
    }

    server {
        listen 8080;
        server_name localhost;

        location ~ ^/packages/(.*)$ {
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_pass http://verdaccio/$1;
            proxy_redirect off;
        }

        location = / {
            return 301 /packages/;
        }
    }
}
