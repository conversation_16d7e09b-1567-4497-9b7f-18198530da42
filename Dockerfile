FROM node:18-alpine

# Install Verdaccio and NGINX
RUN npm install -g verdaccio && apk add --no-cache nginx

# Copy custom config files
COPY config/config.yaml /verdaccio/conf/config.yaml
COPY nginx.conf /etc/nginx/nginx.conf
COPY start.sh /start.sh

# Make start script executable
RUN chmod +x /start.sh

# Create Verdaccio storage dir
RUN mkdir -p /verdaccio/storage

# Expose port for nginx
EXPOSE 8080

# Start both Verdaccio and Nginx
CMD ["/start.sh"]
