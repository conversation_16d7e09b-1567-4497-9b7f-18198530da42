# Verdaccio Standalone Server with /packages Path

A production-ready Verdaccio private npm registry deployment configured to serve from the `/packages` path using nginx reverse proxy.

## 🚀 Quick Start

```bash
# Clone and start
git clone <your-repo>
cd verdaccio-standalone-server
docker-compose up -d

# Access the registry
# Web Interface: http://localhost:8080/packages/
# npm Registry: npm --registry http://localhost:8080/packages/
```

## 📁 Project Structure

```
verdaccio-standalone-server/
├── config/
│   └── config.yaml          # Verdaccio configuration
├── nginx.conf               # Nginx reverse proxy config
├── docker-compose.yml       # Complete deployment setup
├── Dockerfile              # Verdaccio container build
└── README.md               # This file
```

## ⚙️ Configuration

### Verdaccio Configuration (`config/config.yaml`)
- **Storage**: `/verdaccio/storage` (persistent volume)
- **Authentication**: htpasswd (file-based)
- **URL Prefix**: `/packages` (served via nginx proxy)
- **Package Access**: `@ava/*` packages private, all others proxied to npmjs
- **Body Size**: 200mb max upload

### Nginx Configuration (`nginx.conf`)
- **Proxy Path**: `/packages/*` → `verdaccio:4873/*`
- **Headers**: Properly configured for Verdaccio URL generation
- **Port**: External 8080 → Internal 80

## 🛠️ Commands

### Deployment
```bash
# Start services
docker-compose up -d

# Stop services  
docker-compose down

# View logs
docker-compose logs verdaccio
docker-compose logs nginx

# Rebuild after config changes
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

### Usage
```bash
# Set registry for project
echo "registry=http://localhost:8080/packages/" > .npmrc

# View package info
npm --registry http://localhost:8080/packages/ view lodash

# Install packages
npm --registry http://localhost:8080/packages/ install express

# Create user (for publishing)
npm --registry http://localhost:8080/packages/ adduser

# Publish package
npm --registry http://localhost:8080/packages/ publish
```

## 🌐 Access Points

| Service | URL | Purpose |
|---------|-----|---------|
| **Web Interface** | http://localhost:8080/packages/ | Browse packages, manage users |
| **npm Registry** | http://localhost:8080/packages/ | Package installation/publishing |
| **Health Check** | http://localhost:8080/packages/-/ping | Service status |

## 🔧 Development

### Local Testing
```bash
# Test web interface
curl -I http://localhost:8080/packages/

# Test npm functionality
npm --registry http://localhost:8080/packages/ search react

# Test package installation
npm --registry http://localhost:8080/packages/ install lodash --dry-run
```

### Architecture
- **Verdaccio**: Runs on internal port 4873
- **Nginx**: Reverse proxy handling `/packages` path mapping
- **Docker Network**: Internal communication between containers
- **Volume**: Persistent storage for packages and user data

## 📦 Package Scope Configuration

The registry is configured for:
- **`@ava/*` packages**: Private packages (no proxy to npmjs)
- **All other packages**: Proxy to npmjs.org when not found locally

## 🔒 Security Features

- **Authentication required** for publishing
- **Public access** for package installation
- **Rate limiting** enabled via Verdaccio defaults
- **Audit middleware** enabled for security scanning

## 🚀 Production Deployment

For production deployment:
1. Update `VERDACCIO_PUBLIC_URL` in docker-compose.yml
2. Configure proper domain in nginx.conf
3. Add SSL certificates
4. Set up proper authentication (LDAP, etc.)

## 📝 Notes

- The `/packages` URL prefix requires the nginx reverse proxy to work correctly
- Direct Docker run with `url_prefix` won't work without proper proxy setup
- All static assets and API endpoints are properly mapped through the proxy
- The setup is production-ready with persistent storage and proper container networking

